package mp.shapes;

public class APolarPoint implements PolarPointInterface{
	double radius, angle;
	public APolarPoint(double theRadius, double theAngle) {
		radius = theRadius;
		angle = theAngle;
	}
	public APolarPoint(int theX, int theY) {
		radius = Math.sqrt(theX*theX + theY*theY);
		angle = Math.atan((double) theY/theX);
	}
	@Override
	public int getX() { return (int) (radius*Math.cos(angle)); }
	@Override
	public int getY() { return (int) (radius*Math.sin(angle)); }
	@Override
	public double getAngle() { return angle; } 
	@Override
	public double getRadius() { return radius;}
	@Override
	public void setX(int x) {
	}
	@Override
	public void setY(int y) {
	}	
}
