package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class RobinHead implements ImageShape{
    private String location = "images/robin.jpg";
    private int x, y;
    public RobinHead() {
    }
    @Override
    public String getImageFileName() { 
    	return location; 
    }
    @Override
    public void setImageFileName(String file) { 
    	location = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int someX) { 
    	x = someX;
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(int someY) { 
    	y = someY;
    }
}
