package mp.bridge;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class GalahadHead implements ImageShape{
    private String location = "images/galahad.jpg";
    private int x, y;
    public GalahadHead() {
    }
    @Override
    public String getImageFileName() { 
    	return location; 
    }
    @Override
    public void setImageFileName(final String file) { 
    	location = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(final int someX) { 
    	x = someX; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(final int someY) { 
    	y = someY; 
    }
}
