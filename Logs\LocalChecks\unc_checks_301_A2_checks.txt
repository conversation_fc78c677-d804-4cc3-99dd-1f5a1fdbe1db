Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component angle in Identifier SOME_ANGLE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component count in Identifier COUNT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component delta in Identifier DELTA is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component frame in Identifier frame is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component i in Identifier i is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component line in Identifier line is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component radius in Identifier SOME_RADIUS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component seconds in Identifier SLEEP_SECONDS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component sleep in Identifier SLEEP_SECONDS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component some in Identifier SOME_ANGLE is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component some in Identifier SOME_RADIUS is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component start in Identifier START_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component start in Identifier START_Y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component x in Identifier START_X is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Component y in Identifier START_Y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has made expected call @Comp301Tags.FACTORY_CLASS!@bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has not made expected call (.*)!sleep:long->void. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:1: Method:main:String[]->void in class main.Assignment2:[main.Assignment2] has not made expected call @Comp301Tags.FACTORY_CLASS!@consoleSceneViewFactoryMethod:->.*. [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Access modifiers used: Access Modifiers Used: [(main.Assignment2, public, private, 3, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:7 Public Variables Fraction:1.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:0.0 Average Variable Access:0.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.1428571428571428 Average Local References per Variable:1.1428571428571428 Average Local Assignments per Variable:1.1428571428571428 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component assignment in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Component main in Identifier main.Assignment2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  animateLine:->void, static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Type main.Assignment2 matches tags (main.Assignment2)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:29: Named Constant SOME_RADIUS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:32: Named Constant SOME_ANGLE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:12:29: Named Constant START_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:13:29: Named Constant START_Y defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:14:29: Named Constant DELTA defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:15:29: Named Constant COUNT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:16:30: Named Constant SLEEP_SECONDS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:19: Interface RotateLine used as the type of variable/function line. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:32:29: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:33: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component area in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component arthur in Identifier arthur is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier AREA_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier GORGE_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ax in Identifier SOME_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier GUARD_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier KNIGHT_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component ay in Identifier SOME_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GAL_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component constant in Identifier constant is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component cur in Identifier cur is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gal in Identifier GAL_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component galahad in Identifier galahad is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier GORGE_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component gorge in Identifier gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier GUARD_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guard is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component guard in Identifier guardArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component height in Identifier AREA_HEIGHT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier KNIGHT_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightArea is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component knight in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier LANCELOT_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component lancelot in Identifier lancelot is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component number in Identifier number is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component occupied in Identifier occupied is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier ROBIN_CONSTANT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component robin in Identifier robin is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component say in Identifier say is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_AX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component some in Identifier SOME_AY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component turn in Identifier knightTurn is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:0: Component width in Identifier AREA_WIDTH is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Arthur of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Galahad of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Guard of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Lancelot of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected getter for property Robin of type @Comp301Tags.AVATAR in parent type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:1: Expected instantiation of @Comp301Tags.AVATAR in type main.BridgeSceneImpl[@Comp301Tags.BRIDGE_SCENE] by methods [public  BridgeSceneImpl:->]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Access modifiers used: Access Modifiers Used: [(main.BridgeSceneImpl, public, private, 3, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:25 Public Variables Fraction:0.24 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.04 Private  Variable Fraction:0.72 Average Variable Access:2.24 Number of Properties:10 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:2.7058823529411766 Average Local References per Variable:3.16 Average Local Assignments per Variable:3.16 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Component bridge in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Component impl in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Component main in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Component scene in Identifier main.BridgeSceneImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Expected signature approach:@Comp301Tags.AVATAR->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Expected signature failed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Expected signature passed:->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Expected signature say:String->void in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Interfaces defined: [mp.bridge.BridgeScene]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[public  getArthur:->mp.bridge.Avatar, public  getLancelot:->mp.bridge.Avatar, public  getRobin:->mp.bridge.Avatar, public  getGalahad:->mp.bridge.Avatar, public  getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScaleRectangleInterface, public  getGuardArea:->mp.shapes.AScaleRectangleInterface, public  getGorge:->mp.shapes.GetRectangle, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Missing signature @scroll:int;int->void//EC in type main.BridgeSceneImpl:[@Comp301Tags.BRIDGE_SCENE]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Properties defined: Properties:[readonly  p-v:3 access:public KnightTurn:boolean(public , null), readonly  p-v:3 access:public Gorge:mp.shapes.GetRectangle(public , null), readonly  p-v:3 access:public Occupied:boolean(public , null), readonly  p-v:3 access:public Arthur:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public KnightArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:3 access:public Guard:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Lancelot:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public GuardArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:3 access:public Galahad:mp.bridge.Avatar(public , null), readonly  p-v:3 access:public Robin:mp.bridge.Avatar(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Type main.BridgeSceneImpl matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18:1: Class Data Abstraction Coupling is 8 (max allowed is 7) classes [AScaleRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21: Interface Avatar used as the type of variable/function arthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21: Interface Avatar used as the type of variable/function galahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21: Interface Avatar used as the type of variable/function guard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21: Interface Avatar used as the type of variable/function lancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:21: Interface Avatar used as the type of variable/function robin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22:29: Named Constant SOME_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant SOME_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant GAL_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:27:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:28: Interface GetRectangle used as the type of variable/function gorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:29: Interface Avatar used as the type of variable/function cur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:30: Interface AScaleRectangleInterface used as the type of variable/function knightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:31: Interface AScaleRectangleInterface used as the type of variable/function guardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:33:30: Named Constant AREA_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant KNIGHT_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant GUARD_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:37:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:39:30: Named Constant GORGE_AX defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:66: In method failed, rewrite if to use else-ifs  rather than nested ifs (that is, use else branching instead of then branching) [ThenBranching]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:78: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:78:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:84:21: Final parameter say defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:85: In method say, rewrite if to use else-ifs  rather than nested ifs (that is, use else branching instead of then branching) [ThenBranching]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:85: Then part much bulkier than else part of if. Invert if condition to swap then and else blocks. Then#: 60.0. Else#:5.0. Ratio:12.0 [BulkierThen]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:92: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:96: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:100: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:104: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:108: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:112: Interface AScaleRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:116: Interface AScaleRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:120: Interface GetRectangle used as the type of variable/function getGorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component args in Identifier args is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component max in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component printed in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component process in Identifier PROCESS_TIME is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component time in Identifier PROCESS_TIME is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_PRINTED_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:0: Component traces in Identifier MAX_TRACES is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Access modifiers used: Access Modifiers Used: [(main.RunSS25A2Tests, public, private, 3, main.RunSS25A2Tests, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:1.0 Average Local References per Variable:1.0 Average Local Assignments per Variable:1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component a in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component main in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component run in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Component tests in Identifier main.RunSS25A2Tests is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[static public  main:String[]->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIME defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component console in Identifier consoleView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component scene in Identifier scene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:0: Component view in Identifier consoleView is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Access modifiers used: Access Modifiers Used: [(main.StaticFactoryClass, public, package, 2, main.Assignment2, null ), (main.StaticFactoryClass, public, private, 3, main.StaticFactoryClass, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:3 Number of Non Getter Functions:3 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.5 Private  Variable Fraction:0.5 Average Variable Access:2.5 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component class in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component factory in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component main in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Component static in Identifier main.StaticFactoryClass is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @consoleSceneViewFactoryMethod:->.* in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Expected signature @legsFactoryMethod:*->@Comp301Tags.ANGLE in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Interfaces defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Methods defined: NonGetterFunctions:[static public  bridgeSceneFactoryMethod:->mp.bridge.BridgeScene, static public  consoleSceneViewFactoryMethod:->mp.shapes.ConsoleSceneInterface, static public  legsFactoryMethod:->mp.bridge.Angle]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @bridgeSceneControllerFactoryMethod:->@Comp301Tags.BRIDGE_SCENE_CONTROLLER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @delegatingBridgeSceneViewFactoryMethod:->@Comp301Tags.DELEGATING_BRIDGE_SCENE_VIEW in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @inheritingBridgeScenePainterFactoryMethod:->@Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Missing signature @observableBridgeScenePainterFactoryMethod:->@Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER in type main.StaticFactoryClass:[@Comp301Tags.FACTORY_CLASS]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Type main.StaticFactoryClass matches tags (@Comp301Tags.FACTORY_CLASS)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:9: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:11: Interface BridgeScene used as the type of variable/function scene. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:12: Interface ConsoleSceneInterface used as the type of variable/function consoleView. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:14: Interface BridgeScene used as the type of variable/function bridgeSceneFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:19: Interface ConsoleSceneInterface used as the type of variable/function consoleSceneViewFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\StaticFactoryClass.java:26: Interface Angle used as the type of variable/function legsFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Component angle in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Component bridge in Identifier mp.bridge.Angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Super types: [mp.shapes.Moveable, mp.shapes.Get] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Type mp.bridge.Angle matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Angle.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component location in Identifier location is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.ArthurHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: No method in class mp.bridge.ArthurHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between ArthurHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:6:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Access modifiers used: Access Modifiers Used: [(mp.bridge.ArthurHead, public, private, 3, mp.bridge.ArthurHead, null ), (mp.bridge.ArthurHead, public, public, 0, main.Assignment2, null ), (mp.bridge.ArthurHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.ArthurHead, public, package, 2, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Component arthur in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Component bridge in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Component head in Identifier mp.bridge.ArthurHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Interfaces defined: [mp.bridge.ImageShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Missing signature move:int;int->void in type mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Missing signature scale:double->void//EC in type mp.bridge.ArthurHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Type mp.bridge.ArthurHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:8: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:20:34: Parameter file should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:23: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:27: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:28:22: Parameter someX should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:31: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:35: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:36:22: Parameter someY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Access modifiers used: Access Modifiers Used: [(mp.bridge.Avatar, public, public, 0, main.Assignment2, null ), (mp.bridge.Avatar, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:5 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:4 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Component avatar in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Component bridge in Identifier mp.bridge.Avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default move:int;int->void]Getters:[default getHead:->mp.bridge.ImageShape, default getStringShape:->mp.bridge.StringShape, default getArms:->mp.bridge.Angle, default getLegs:->mp.bridge.Angle]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Properties defined: Properties:[readonly  p-v:5 access:package Legs:mp.bridge.Angle(default , null), readonly  p-v:5 access:package Head:mp.bridge.ImageShape(default , null), readonly  p-v:5 access:package StringShape:mp.bridge.StringShape(default , null), readonly  p-v:5 access:package Arms:mp.bridge.Angle(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Super types: [mp.shapes.Moveable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Type mp.bridge.Avatar matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:11: Interface ImageShape used as the type of variable/function getHead. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:12: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:13: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Avatar.java:14: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component arms in Identifier arms is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component head in Identifier head is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component legs in Identifier legs is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component speech in Identifier speech is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Expected getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR]. Good! [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.AvatarImpl[@Comp301Tags.AVATAR] [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:1: Some method (AvatarImpl:mp.bridge.ImageShape->) in class mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR] has made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. Good! [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:7:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:8:8: Unused import - mp.shapes.Moveable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.AvatarImpl, public, public, 0, main.Assignment2, null ), (mp.bridge.AvatarImpl, public, private, 3, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:1 Public Methods Fraction:0.8333333333333334 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.16666666666666666 Average Method Access:0.5 Number of Variables:4 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:4.5 Average Local References per Variable:4.5 Average Local Assignments per Variable:4.5 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component avatar in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component bridge in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Component impl in Identifier mp.bridge.AvatarImpl is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Expected signature move:int;int->void in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Interfaces defined: [mp.bridge.Avatar]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[private  layoutAtOrigin:->void, public  move:int;int->void]Getters:[public  getHead:->mp.bridge.ImageShape, public  getStringShape:->mp.bridge.StringShape, public  getArms:->mp.bridge.Angle, public  getLegs:->mp.bridge.Angle]Setters:[]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Missing signature scale:double->void//EC in type mp.bridge.AvatarImpl:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Properties defined: Properties:[readonly  p-v:3 access:public Legs:mp.bridge.Angle(public , null), readonly  p-v:3 access:public Head:mp.bridge.ImageShape(public , null), readonly  p-v:3 access:public StringShape:mp.bridge.StringShape(public , null), readonly  p-v:3 access:public Arms:mp.bridge.Angle(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Type mp.bridge.AvatarImpl matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:13: Interface ImageShape used as the type of variable/function head. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:14: Interface StringShape used as the type of variable/function speech. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:15: Interface Angle used as the type of variable/function arms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:16: Interface Angle used as the type of variable/function legs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18: Interface ImageShape used as the type of variable/function h. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18:23: Final parameter h defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:28: Interface ImageShape used as the type of variable/function getHead. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:32: Interface StringShape used as the type of variable/function getStringShape. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:36: Interface Angle used as the type of variable/function getArms. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:40: Interface Angle used as the type of variable/function getLegs. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:43: Signatures public  move:int;int->void common with mp.bridge.Shape defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:43: Signatures public  move:int;int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component avatar in Identifier avatar is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:0: Component say in Identifier say is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:14 Number of Functions:10 Number of Non Getter Functions:0 Number of Getters and Setters:10 Number of Non Public Methods:5 Public Methods Fraction:0.6428571428571429 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.35714285714285715 Private  Methods Fraction:0.0 Average Method Access:0.7142857142857143 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:10 Public Properties Fraction:0.5 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.5 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:1.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component bridge in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Component scene in Identifier mp.bridge.BridgeScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  passed:->void, public  failed:->void, public  approach:mp.bridge.Avatar->void, public  say:String->void]Getters:[default getArthur:->mp.bridge.Avatar, default getLancelot:->mp.bridge.Avatar, default getRobin:->mp.bridge.Avatar, default getGalahad:->mp.bridge.Avatar, default getGuard:->mp.bridge.Avatar, public  getKnightArea:->mp.shapes.AScaleRectangleInterface, public  getGuardArea:->mp.shapes.AScaleRectangleInterface, public  getGorge:->mp.shapes.GetRectangle, public  getOccupied:->boolean, public  getKnightTurn:->boolean]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Properties defined: Properties:[readonly  p-v:5 access:public KnightTurn:boolean(public , null), readonly  p-v:5 access:public Gorge:mp.shapes.GetRectangle(public , null), readonly  p-v:5 access:public Occupied:boolean(public , null), readonly  p-v:5 access:package Arthur:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public KnightArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:5 access:package Guard:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Lancelot:mp.bridge.Avatar(default , null), readonly  p-v:5 access:public GuardArea:mp.shapes.AScaleRectangleInterface(public , null), readonly  p-v:5 access:package Galahad:mp.bridge.Avatar(default , null), readonly  p-v:5 access:package Robin:mp.bridge.Avatar(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Type mp.bridge.BridgeScene matches tags (@Comp301Tags.BRIDGE_SCENE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:13: Interface Avatar used as the type of variable/function getArthur. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:14: Interface Avatar used as the type of variable/function getLancelot. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:15: Interface Avatar used as the type of variable/function getRobin. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:16: Interface Avatar used as the type of variable/function getGalahad. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:17: Interface Avatar used as the type of variable/function getGuard. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:20: Interface Avatar used as the type of variable/function avatar. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:22: Interface AScaleRectangleInterface used as the type of variable/function getKnightArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:23: Interface AScaleRectangleInterface used as the type of variable/function getGuardArea. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\BridgeScene.java:24: Interface GetRectangle used as the type of variable/function getGorge. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component location in Identifier location is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.GalahadHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: No method in class mp.bridge.GalahadHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GalahadHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:5:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Access modifiers used: Access Modifiers Used: [(mp.bridge.GalahadHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GalahadHead, public, private, 3, mp.bridge.GalahadHead, null ), (mp.bridge.GalahadHead, public, public, 0, main.BridgeSceneImpl, null ), (mp.bridge.GalahadHead, public, package, 2, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Component bridge in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Component galahad in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Component head in Identifier mp.bridge.GalahadHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Interfaces defined: [mp.bridge.ImageShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Missing signature move:int;int->void in type mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Missing signature scale:double->void//EC in type mp.bridge.GalahadHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Type mp.bridge.GalahadHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:7: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:14: Signatures public  getImageFileName:->String common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:18: Signatures public  setImageFileName:String->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:19:34: Final parameter file defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:22: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:26: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:26: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:26: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:27:22: Final parameter someX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:30: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:30: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:30: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:34: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:34: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:34: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:35:22: Final parameter someY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component location in Identifier location is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.GuardHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: No method in class mp.bridge.GuardHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between GuardHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Access modifiers used: Access Modifiers Used: [(mp.bridge.GuardHead, public, public, 0, main.Assignment2, null ), (mp.bridge.GuardHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.GuardHead, public, private, 3, mp.bridge.GuardHead, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Component bridge in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Component guard in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Component head in Identifier mp.bridge.GuardHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Interfaces defined: [mp.bridge.ImageShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Missing signature move:int;int->void in type mp.bridge.GuardHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Missing signature scale:double->void//EC in type mp.bridge.GuardHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Type mp.bridge.GuardHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:13: Signatures public  getImageFileName:->String common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:13: Signatures public  getImageFileName:->String common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:13: Signatures public  getImageFileName:->String common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:13: Signatures public  getImageFileName:->String common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:17: Signatures public  setImageFileName:String->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:17: Signatures public  setImageFileName:String->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:17: Signatures public  setImageFileName:String->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:17: Signatures public  setImageFileName:String->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:18:34: Final parameter file defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.shapes.AScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:21: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.shapes.AScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:25: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:26:22: Parameter someX should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.shapes.AScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:29: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.shapes.AScaleRectangle defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:33: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:34:22: Parameter someY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component bridge in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component image in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Component shape in Identifier mp.bridge.ImageShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getImageFileName:->String]Setters:[default setImageFileName:String->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package ImageFileName:String(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Type mp.bridge.ImageShape matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ImageShape.java:7: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component location in Identifier location is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.LancelotHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: No method in class mp.bridge.LancelotHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between LancelotHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:6:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Access modifiers used: Access Modifiers Used: [(mp.bridge.LancelotHead, public, public, 0, main.Assignment2, null ), (mp.bridge.LancelotHead, public, private, 3, mp.bridge.LancelotHead, null ), (mp.bridge.LancelotHead, public, package, 2, mp.bridge.AvatarImpl, null ), (mp.bridge.LancelotHead, public, public, 0, mp.shapes.RotatingLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Component bridge in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Component head in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Component lancelot in Identifier mp.bridge.LancelotHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Interfaces defined: [mp.bridge.ImageShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Missing signature move:int;int->void in type mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Missing signature scale:double->void//EC in type mp.bridge.LancelotHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Type mp.bridge.LancelotHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:8: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:15: Signatures public  getImageFileName:->String common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:15: Signatures public  getImageFileName:->String common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:19: Signatures public  setImageFileName:String->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:19: Signatures public  setImageFileName:String->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:20:34: Parameter file should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:23: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:23: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:23: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:23: Signatures public  getX:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:23: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setX:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:27: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:28:22: Parameter someX should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31: Signatures public  getY:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:31: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:35: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:35: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:35: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:35: Signatures public  setY:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:35: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:36:22: Parameter someY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component file in Identifier file is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component location in Identifier location is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Arms of type @Comp301Tags.ANGLE in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Head of type @IMAGE_PATTERN in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property Legs of type @Comp301Tags.ANGLE in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Missing getter for property StringShape of type @STRING_PATTERN in parent type mp.bridge.RobinHead[@Comp301Tags.AVATAR] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: No method in class mp.bridge.RobinHead:[@Comp301Tags.AVATAR] has not made expected call @Comp301Tags.FACTORY_CLASS!@legsFactoryMethod:*->@Comp301Tags.ANGLE. [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RobinHead and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Access modifiers used: Access Modifiers Used: [(mp.bridge.RobinHead, public, public, 0, main.Assignment2, null ), (mp.bridge.RobinHead, public, private, 3, mp.bridge.RobinHead, null ), (mp.bridge.RobinHead, public, package, 2, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Component bridge in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Component head in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Component robin in Identifier mp.bridge.RobinHead is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Interfaces defined: [mp.bridge.ImageShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getImageFileName:->String, public  getX:->int, public  getY:->int]Setters:[public  setImageFileName:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Missing signature move:int;int->void in type mp.bridge.RobinHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Missing signature scale:double->void//EC in type mp.bridge.RobinHead:[@Comp301Tags.AVATAR]. [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public ImageFileName:String(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Type mp.bridge.RobinHead matches tags (@Comp301Tags.AVATAR)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:7: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:14: Signatures public  getImageFileName:->String common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:14: Signatures public  getImageFileName:->String common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:14: Signatures public  getImageFileName:->String common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:18: Signatures public  setImageFileName:String->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:18: Signatures public  setImageFileName:String->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:18: Signatures public  setImageFileName:String->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:19:34: Parameter file should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:22: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:26: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:22: Parameter someX should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:30: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.bridge.ImageShape, mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:34: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:35:22: Parameter someY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component left in Identifier left is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component right in Identifier right is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Missing getter for property LeftLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.Shape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Missing getter for property RightLine of type @Comp301Tags.ROTATING_LINE in parent type mp.bridge.Shape[@Comp301Tags.ANGLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Property readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null) common between Shape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:1: Property readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null) common between Shape and mp.shapes.Gorge not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:2:8: Unused import - mp.shapes.Get. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Access modifiers used: Access Modifiers Used: [(mp.bridge.Shape, public, public, 0, main.Assignment2, null ), (mp.bridge.Shape, public, private, 3, mp.bridge.Shape, null ), (mp.bridge.Shape, public, package, 2, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:3.0 Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Component bridge in Identifier mp.bridge.Shape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Component shape in Identifier mp.bridge.Shape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Expected signature move:int;int->void in type mp.bridge.Shape:[@Comp301Tags.ANGLE]. Good! [ExpectedSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Interfaces defined: [mp.bridge.Angle]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Properties defined: Properties:[readonly  p-v:3 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:3 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Type mp.bridge.Shape matches tags (@Comp301Tags.ANGLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:10: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:13: Interface RotateLine used as the type of variable/function left. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:13: Interface RotateLine used as the type of variable/function right. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:18: Signatures public  getLeftLine:->mp.shapes.RotateLine common with mp.shapes.Gorge defined in common types [mp.shapes.Get]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:19: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:22: Signatures public  getRightLine:->mp.shapes.RotateLine common with mp.shapes.Gorge defined in common types [mp.shapes.Get]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:23: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:26: Signatures public  move:int;int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Moveable]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:27:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:27:34: Parameter deltaY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component at in Identifier at is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component ax in Identifier ax is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component ay in Identifier ay is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component text in Identifier text is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between SpeechBubble and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Access modifiers used: Access Modifiers Used: [(mp.bridge.SpeechBubble, public, private, 3, mp.bridge.SpeechBubble, null ), (mp.bridge.SpeechBubble, public, public, 0, main.Assignment2, null ), (mp.bridge.SpeechBubble, public, package, 2, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:3 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.0 Average Local Assignments per Variable:2.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component bridge in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component bubble in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Component speech in Identifier mp.bridge.SpeechBubble is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Interfaces defined: [mp.bridge.StringShape]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getText:->String, public  getX:->int, public  getY:->int]Setters:[public  setText:String->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), editable, g-s:0 p-v:3 access:public Text:String(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:3: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13:25: Parameter at should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:16: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:16: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:20: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:20: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:21:22: Final parameter ax defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:24: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:24: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:28: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:28: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:29:22: Final parameter ay defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:0: Component at in Identifier at is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Access modifiers used: Access Modifiers Used: [(mp.bridge.StringShape, public, public, 0, main.BridgeSceneImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:2 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component bridge in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component shape in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Component string in Identifier mp.bridge.StringShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getText:->String]Setters:[default setText:String->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Text:String(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\StringShape.java:5: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Setter setX does not assign to a global variable [SetterAssignsGlobal]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:0: Setter setY does not assign to a global variable [SetterAssignsGlobal]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public X:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property editable, g-s:0 p-v:5 access:public Y:int(public ,public ) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:2 access:public Angle:double(public , null) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:1: Property readonly  p-v:2 access:public Radius:double(public , null) common between APolarPoint and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.APolarPoint, public, public, 0, main.Assignment2, null ), (mp.shapes.APolarPoint, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.APolarPoint, public, private, 3, mp.shapes.APolarPoint, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:6 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:6 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:2 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.5 ReadOnly Access Properties Fraction:0.5 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component a in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component point in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component polar in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Component shapes in Identifier mp.shapes.APolarPoint is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Interfaces defined: [mp.shapes.PolarPointInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getX:->int, public  getY:->int, public  getAngle:->double, public  getRadius:->double]Setters:[public  setX:int->void, public  setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Properties defined: Properties:[readonly  p-v:2 access:public Radius:double(public , null), readonly  p-v:2 access:public Angle:double(public , null), editable, g-s:0 p-v:5 access:public X:int(public ,public ), editable, g-s:0 p-v:5 access:public Y:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:3: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:5:28: Parameter theRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:5:46: Parameter theAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:9:28: Parameter theX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:9:38: Parameter theY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:13: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:13: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:13: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:13: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:15: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:15: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:15: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:15: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:17: Signatures public  getAngle:->double common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.PolarPointInterface]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:19: Signatures public  getRadius:->double common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.PolarPointInterface]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:21: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:21: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:21: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:21: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.PolarPointInterface]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:22:26: Parameter x should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:24: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:24: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:24: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:24: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.PolarPointInterface]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:25:26: Parameter y should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component conversion in Identifier percentConversion is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component height in Identifier someHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component percent in Identifier percentConversion is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component some in Identifier someHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component some in Identifier someWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component some in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component some in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component width in Identifier someWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component x in Identifier someX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component y in Identifier someY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected getter for property Height of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected getter for property Width of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected setter for property Height of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Expected setter for property Width of type int in parent type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Missing instantiation of java.beans.PropertyChangeEvent in type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE]. [ExpectedClassInstantiations]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: No method in class mp.shapes.AScaleRectangle:[@Comp301Tags.BOUNDED_SHAPE] has not made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). [MissingMethodCall]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Height:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Width:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public X:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:1: Property editable, g-s:0 p-v:2 access:public Y:int(public ,public ) common between AScaleRectangle and mp.shapes.RotatingLine not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.AScaleRectangle, public, private, 3, mp.shapes.AScaleRectangle, null ), (mp.shapes.AScaleRectangle, public, public, 0, main.Assignment2, null ), (mp.shapes.AScaleRectangle, public, public, 0, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:9 Number of Functions:4 Number of Non Getter Functions:0 Number of Getters and Setters:8 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:5 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:4 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:3.6 Average Local Assignments per Variable:3.6 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Component a in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Component rectangle in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Component scale in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Component shapes in Identifier mp.shapes.AScaleRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Interfaces defined: [mp.shapes.AScaleRectangleInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int]Setters:[public  setHeight:int->void, public  setWidth:int->void, public  setX:int->void, public  setY:int->void]  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Missing supertype: @Comp301Tags.LOCATABLE of type mp.shapes.AScaleRectangle[@Comp301Tags.BOUNDED_SHAPE] [ExpectedSuperTypes]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Properties defined: Properties:[editable, g-s:0 p-v:2 access:public X:int(public ,public ), editable, g-s:0 p-v:2 access:public Y:int(public ,public ), editable, g-s:0 p-v:2 access:public Height:int(public ,public ), editable, g-s:0 p-v:2 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Type mp.shapes.AScaleRectangle matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:6: Variables defined: {10}  [STBuilder]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:32: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:39: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:46: Parameter w should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:53: Parameter h should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:17: Signatures public  getX:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:19: Signatures public  getY:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:21: Signatures public  getWidth:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:23: Signatures public  getHeight:->int common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:25: Signatures public  setHeight:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:26:31: Parameter someHeight should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:27: Signatures public  setWidth:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:28:30: Parameter someWidth should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:30:27: Parameter percentage should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:34: Signatures public  setX:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:35:26: Parameter someX should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.bridge.ArthurHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.bridge.GalahadHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.bridge.LancelotHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.bridge.RobinHead defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.bridge.SpeechBubble defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.shapes.APolarPoint defined in common types [mp.shapes.Locatable]. Good! [PeerCommonSignatures]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:38: Signatures public  setY:int->void common with mp.shapes.RotatingLine defined in common types [mp.shapes.Locatable, mp.shapes.BoundedShape]. Good! [PeerCommonSignatures]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:39:26: Parameter someY should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:0: Component percentage in Identifier percentage is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:1.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component a in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component interface in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component rectangle in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component scale in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Component shapes in Identifier mp.shapes.AScaleRectangleInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  scale:int->void]Getters:[]Setters:[public  setHeight:int->void, public  setWidth:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Properties defined: Properties:[writeonly  p-v:5 access:public Height:int( null,public ), writeonly  p-v:5 access:public Width:int( null,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Super types: [mp.shapes.BoundedShape] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Type mp.shapes.AScaleRectangleInterface matches tags (@Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangleInterface.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component bounded in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component shape in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Component shapes in Identifier mp.shapes.BoundedShape is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getWidth:->int, default getHeight:->int]Setters:[default setWidth:int->void, default setHeight:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package Height:int(default ,default ), editable, g-s:0 p-v:5 access:package Width:int(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Type mp.shapes.BoundedShape matches tags (@Comp301Tags.LOCATABLE + @Comp301Tags.BOUNDED_SHAPE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\BoundedShape.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component evt in Identifier evt is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:0: Component instance in Identifier instance is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:1: All public methods override. Good!  [PublicMethodsOverride]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Access modifiers used: Access Modifiers Used: [(mp.shapes.ConsoleScene, public, private, 3, mp.shapes.ConsoleScene, null ), (mp.shapes.ConsoleScene, public, public, 0, main.StaticFactoryClass, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:1 Number of Non Getter Functions:1 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:1 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:NaN Average Local References per Variable:3.0 Average Local Assignments per Variable:3.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Component console in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Component scene in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Component shapes in Identifier mp.shapes.ConsoleScene is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Interfaces defined: [mp.shapes.ConsoleSceneInterface]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Methods defined: NonGetterFunctions:[static public  consoleSceneViewFactoryMethod:->mp.shapes.ConsoleSceneInterface]NonSetterProcedures:[public  propertyChange:java.beans.PropertyChangeEvent->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:5: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:6: Interface ConsoleSceneInterface used as the type of variable/function instance. Good! [VariableHasClassType]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:8:36: Parameter evt should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:11: Interface ConsoleSceneInterface used as the type of variable/function consoleSceneViewFactoryMethod. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:0 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:NaN Protected Methods Fraction:NaN Package Access Methods Fraction:NaN Private  Methods Fraction:NaN Average Method Access:NaN Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component console in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component interface in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component scene in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Component shapes in Identifier mp.shapes.ConsoleSceneInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Super types: [java.beans.PropertyChangeListener] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Type mp.shapes.ConsoleSceneInterface matches tags (@Comp301Tags.CONSOLE_SCENE_VIEW)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleSceneInterface.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Component get in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Component shapes in Identifier mp.shapes.Get is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Properties defined: Properties:[readonly  p-v:5 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:5 access:public LeftLine:mp.shapes.RotateLine(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:4: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Get.java:5: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Access modifiers used: Access Modifiers Used: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:1 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:1 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component get in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component rectangle in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Component shapes in Identifier mp.shapes.GetRectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getRectangle:->mp.shapes.AScaleRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Properties defined: Properties:[readonly  p-v:5 access:public Rectangle:mp.shapes.AScaleRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Super types: [mp.shapes.Get] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\GetRectangle.java:4: Interface AScaleRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component left in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier leftLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component line in Identifier rightLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component lower in Identifier lower is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component num in Identifier num1 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component num in Identifier num2 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component rectangle in Identifier rectangle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component right in Identifier rightLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component start in Identifier start is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component top in Identifier top is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:0: Component upper in Identifier upper is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.Shape not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:1: Property readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null) common between Gorge and mp.bridge.Shape not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Gorge, public, private, 3, mp.shapes.Gorge, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:3 Number of Functions:3 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:10 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:1.0 Private  Variable Fraction:0.0 Average Variable Access:2.0 Number of Properties:3 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:NaN Average Local References per Variable:2.8 Average Local Assignments per Variable:2.8 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Component gorge in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Component shapes in Identifier mp.shapes.Gorge is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Interfaces defined: [mp.shapes.GetRectangle]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getLeftLine:->mp.shapes.RotateLine, public  getRightLine:->mp.shapes.RotateLine, public  getRectangle:->mp.shapes.AScaleRectangleInterface]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Properties defined: Properties:[readonly  p-v:2 access:public RightLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public LeftLine:mp.shapes.RotateLine(public , null), readonly  p-v:2 access:public Rectangle:mp.shapes.AScaleRectangleInterface(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:11: Interface RotateLine used as the type of variable/function leftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:12: Interface RotateLine used as the type of variable/function rightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:13: Interface AScaleRectangleInterface used as the type of variable/function rectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:14:18: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:32: Interface RotateLine used as the type of variable/function getLeftLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:36: Interface RotateLine used as the type of variable/function getRightLine. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:40: Interface AScaleRectangleInterface used as the type of variable/function getRectangle. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.Locatable, public, public, 0, main.Assignment2, null ), (mp.shapes.Locatable, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.Locatable, public, package, 2, mp.shapes.RotatingLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:4 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:4 Number of Non Public Methods:4 Public Methods Fraction:0.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:1.0 Private  Methods Fraction:0.0 Average Method Access:2.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Component locatable in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Component shapes in Identifier mp.shapes.Locatable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[default getX:->int, default getY:->int]Setters:[default setX:int->void, default setY:int->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Properties defined: Properties:[editable, g-s:0 p-v:5 access:package X:int(default ,default ), editable, g-s:0 p-v:5 access:package Y:int(default ,default )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Type mp.shapes.Locatable matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Locatable.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.Moveable, public, public, 0, main.Assignment2, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.Shape, null ), (mp.shapes.Moveable, public, protected, 1, mp.bridge.AvatarImpl, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:1 Number of Functions:0 Number of Non Getter Functions:0 Number of Getters and Setters:0 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:0 Public Properties Fraction:NaN Protected Properties Fraction:NaN Package Access Properties Fraction:NaN Private  Properties Fraction:NaN Editable Properties Fraction:NaN ReadOnly Access Properties Fraction:NaN WriteOnly  Properties Fraction:NaN Average Properties Access:NaN Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component moveable in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Component shapes in Identifier mp.shapes.Moveable is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  move:int;int->void]Getters:[]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Properties defined: Properties:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Super types: [] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Moveable.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Access modifiers used: Access Modifiers Used: [(mp.shapes.PolarPointInterface, public, package, 2, mp.shapes.RotatingLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:2 Number of Functions:2 Number of Non Getter Functions:0 Number of Getters and Setters:2 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:2 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:1.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component interface in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component point in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component polar in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Component shapes in Identifier mp.shapes.PolarPointInterface is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[]Getters:[public  getAngle:->double, public  getRadius:->double]Setters:[]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Properties defined: Properties:[readonly  p-v:5 access:public Radius:double(public , null), readonly  p-v:5 access:public Angle:double(public , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Super types: [mp.shapes.Locatable] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Type None matches tags ({1})  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\PolarPointInterface.java:3: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotateLine, public, public, 0, main.Assignment2, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:0 Number of Methods:5 Number of Functions:1 Number of Non Getter Functions:0 Number of Getters and Setters:3 Number of Non Public Methods:4 Public Methods Fraction:0.2 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.8 Private  Methods Fraction:0.0 Average Method Access:1.6 Number of Variables:0 Public Variables Fraction:NaN Protected Variables Fraction:NaN Package Access Variables Fraction:NaN Private  Variable Fraction:NaN Average Variable Access:NaN Number of Properties:3 Public Properties Fraction:0.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:1.0 Private  Properties Fraction:0.0 Editable Properties Fraction:0.0 ReadOnly Access Properties Fraction:0.3333333333333333 WriteOnly  Properties Fraction:0.6666666666666666 Average Properties Access:2.0 Average Local References per Constant:-1.0 Average Local References per Variable:-1.0 Average Local Assignments per Variable:-1.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component line in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component rotate in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Component shapes in Identifier mp.shapes.RotateLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[default rotate:int->void, public  notify:String;Object;Object->void]Getters:[default getHeight:->int]Setters:[default setRadius:double->void, default setAngle:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Properties defined: Properties:[writeonly  p-v:5 access:package Radius:double( null,default ), writeonly  p-v:5 access:package Angle:double( null,default ), readonly  p-v:5 access:package Height:int(default , null)]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Super types: [mp.shapes.BoundedShape, mp.shapes.Moveable, mp.shapes.PolarPointInterface] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Type mp.shapes.RotateLine matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotateLine.java:6: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component angle in Identifier angle is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component arg in Identifier arg0 is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component ax in Identifier ax is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component ay in Identifier ay is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component change in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component current in Identifier current is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component delta in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component delta in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component event in Identifier event is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component height in Identifier height is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component height in Identifier someHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component listeners in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component old in Identifier old is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component point in Identifier point is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component property in Identifier property is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component property in Identifier propertyChangeListeners is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component radius in Identifier radius is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component some in Identifier some is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component some in Identifier someHeight is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component some in Identifier someWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component unit in Identifier UNIT is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component units in Identifier units is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component width in Identifier someWidth is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component width in Identifier width is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component x in Identifier deltaX is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component x in Identifier x is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component y in Identifier deltaY is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:0: Component y in Identifier y is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: All public methods override. Good!  [PublicMethodsOverride]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Class mp.shapes.RotatingLine with instance methods does not have exactly one interface.       [ClassHasOneInterface]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Consider separating the following method sets into different types [[public  getWidth:->int, public  getY:->int, public  setAngle:double->void, public  setX:int->void, public  getAngle:->double, public  move:int;int->void, public  getX:->int, public  getRadius:->double, public  rotate:int->void, public  setY:int->void, public  getHeight:->int, public  setRadius:double->void], [public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void]] [ClassDecomposition]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected getter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedGetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected instantiation of java.beans.PropertyChangeEvent in type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE] by methods [public  notify:String;Object;Object->void]. Good! [ExpectedClassInstantiations]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property X of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Expected setter for property Y of type int in parent type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedSetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Missing getter for property PropertyChangeListeners of type List in parent type mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE] [ExpectedGetters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Angle:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Height:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Radius:double(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Width:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public X:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.ArthurHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GalahadHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.GuardHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.LancelotHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.RobinHead not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.bridge.SpeechBubble not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.APolarPoint not inherited. [PeerCommonProperties]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Property editable, g-s:0 p-v:3 access:public Y:int(public ,public ) common between RotatingLine and mp.shapes.AScaleRectangle not inherited. [PeerCommonProperties]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:1: Some method (notify:String;Object;Object->void) in class mp.shapes.RotatingLine:[@Comp301Tags.LOCATABLE] has made expected call (.*)!(.*):java.beans.PropertyChangeEvent->(.*). Good! [MissingMethodCall]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Access modifiers used: Access Modifiers Used: [(mp.shapes.RotatingLine, public, public, 0, main.Assignment2, null ), (mp.shapes.RotatingLine, public, public, 0, mp.bridge.AvatarImpl, null ), (mp.shapes.RotatingLine, public, private, 3, mp.shapes.RotatingLine, null )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Aggregate statistics:  Is Abstract:false Number of Abstract Methods:0 Is Generic:false Number of Generic Methods:0 Number of Asserts:0 Number of Ternary Conditionals:1 Number of Methods:16 Number of Functions:6 Number of Non Getter Functions:0 Number of Getters and Setters:12 Number of Non Public Methods:0 Public Methods Fraction:1.0 Protected Methods Fraction:0.0 Package Access Methods Fraction:0.0 Private  Methods Fraction:0.0 Average Method Access:0.0 Number of Variables:5 Public Variables Fraction:0.0 Protected Variables Fraction:0.0 Package Access Variables Fraction:0.0 Private  Variable Fraction:1.0 Average Variable Access:3.0 Number of Properties:6 Public Properties Fraction:1.0 Protected Properties Fraction:0.0 Package Access Properties Fraction:0.0 Private  Properties Fraction:0.0 Editable Properties Fraction:1.0 ReadOnly Access Properties Fraction:0.0 WriteOnly  Properties Fraction:0.0 Average Properties Access:0.0 Average Local References per Constant:1.5 Average Local References per Variable:5.0 Average Local Assignments per Variable:5.0 [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component line in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component rotating in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Component shapes in Identifier mp.shapes.RotatingLine is in dictionary. Good! [MnemonicName]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Expected interface util.models.PropertyListenerRegisterer of class mp.shapes.RotatingLine[@Comp301Tags.LOCATABLE]. Good! [ExpectedInterfaces]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Inner types defined: []  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Interfaces defined: [mp.shapes.RotateLine, util.models.PropertyListenerRegisterer]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Methods defined: NonGetterFunctions:[]NonSetterProcedures:[public  addPropertyChangeListener:java.beans.PropertyChangeListener->void, public  notify:String;Object;Object->void, public  rotate:int->void, public  move:int;int->void]Getters:[public  getX:->int, public  getY:->int, public  getWidth:->int, public  getHeight:->int, public  getRadius:->double, public  getAngle:->double]Setters:[public  setX:int->void, public  setY:int->void, public  setHeight:int->void, public  setWidth:int->void, public  setRadius:double->void, public  setAngle:double->void]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Properties defined: Properties:[editable, g-s:0 p-v:3 access:public Radius:double(public ,public ), editable, g-s:0 p-v:3 access:public Angle:double(public ,public ), editable, g-s:0 p-v:3 access:public X:int(public ,public ), editable, g-s:0 p-v:3 access:public Y:int(public ,public ), editable, g-s:0 p-v:3 access:public Height:int(public ,public ), editable, g-s:0 p-v:3 access:public Width:int(public ,public )]  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Super types: None [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Type mp.shapes.RotatingLine matches tags (@Comp301Tags.LOCATABLE)  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:12: Variables defined: {10}  [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:15: Interface PolarPointInterface used as the type of variable/function point. Good! [VariableHasClassType]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:17:33: Named Constant UNIT defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:21:47: Parameter arg0 should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:26:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:39:22: Final parameter ax defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:48:22: Final parameter ay defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:61:27: Parameter someHeight should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:61:50: The String "Height" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63:26: Parameter someWidth should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63:48: The String "Width" appears 2 times in the file. [MultipleStringLiterals]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69:27: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:77:26: Final parameter angle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:81:24: Final parameter units defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:85:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:85:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:86:13: Variable 'height' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:87:13: Variable 'width' should be declared final. [FinalLocalVariable]
Audit done.
