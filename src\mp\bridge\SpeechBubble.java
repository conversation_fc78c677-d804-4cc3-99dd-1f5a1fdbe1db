package mp.bridge;

public class SpeechBubble implements StringShape{
    private String text = "Grail";
    private int x, y;
    public SpeechBubble() {
    }
    @Override
    public String getText() { 
    	return text; 
    }
    @Override
    public void setText(String at) { 
    	text = at; 
    }
    @Override
    public int getX() { 
    	return x;
    }
    @Override
    public void setX(final int ax) { 
    	x = ax; 
    }
    @Override
    public int getY() { 
    	return y; 
    }
    @Override
    public void setY(final int ay) { 
    	y = ay; 
    }
}
