<Events startTimestamp="1752109572204" logVersion="1.0.0.202503121800">
  <Command __id="9" _type="ShellCommand" date="Wed Jul 09 21:07:39 EDT 2025" starttimestamp="1752109572204" timestamp="87692" type="ECLIPSE_GAINED_FOCUS" />
  <Command __id="10" _type="ShellCommand" date="Wed Jul 09 21:07:56 EDT 2025" starttimestamp="1752109572204" timestamp="104698" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="15" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 21:08:38 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752109572204" timestamp="145917" type="Run" />
  <Command __id="16" _type="ShellCommand" date="Wed Jul 09 21:08:43 EDT 2025" starttimestamp="1752109572204" timestamp="150842" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="21" _type="RunCommand" className="/Assn2/src/main/RunSS25A2Tests.java" date="Wed Jul 09 21:08:57 EDT 2025" kind="Run" projectName="Assn2" starttimestamp="1752109572204" timestamp="164809" type="Run" />
  <Command __id="22" _type="ShellCommand" date="Wed Jul 09 21:09:01 EDT 2025" starttimestamp="1752109572204" timestamp="169088" type="ECLIPSE_LOST_FOCUS" />
  <Command __id="23" _type="ConsoleOutput" date="Wed Jul 09 21:09:17 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="184908" type="ConsoleOutput">
    <outputString><![CDATA[>>Running suite A2Style
<<
]]></outputString>
    <diff><![CDATA[null]]></diff>
  </Command>
  <Command __id="24" _type="ConsoleOutput" date="Wed Jul 09 21:09:17 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="184969" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:17 EDT 2025<<
>>Running test A2PackageDeclarations
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Running suite A2Style"), Diff(INSERT,"Wed Jul 09 21:09:17 EDT 2025<<
¶>>Running test A2PackageDeclarations"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="25" _type="ConsoleOutput" date="Wed Jul 09 21:09:17 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="184984" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:17 EDT 2025<<
>>Running test BridgeSceneDynamics
<<
>>Wed Jul 09 21:09:17 EDT 2025<<
>>Running test BridgeSceneApproachMethodDefined
<<
>>Running checkstyle, this will take time<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>Wed Jul 09 21:09:17 EDT 2025<<
¶>>Running test "), Diff(DELETE,"A2PackageDeclarations
¶"), Diff(INSERT,"BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 21:09:17 EDT 2025<<
¶>>Running test BridgeSceneApproachMethodDefined
¶<<
¶>>Running checkstyle, this will take time"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="26" _type="ConsoleOutput" date="Wed Jul 09 21:09:17 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="185051" type="ConsoleOutput">
    <outputString><![CDATA[WARNING: A terminally deprecated method in java.lang.System has been called
WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
WARNING: System::setSecurityManager will be removed in a future release
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>Wed Jul 09 21:09:17 EDT 2025<<
¶>>Running test BridgeSceneDynamics
¶<<
¶>>Wed Jul 09 21:09:17 EDT 2025<<
¶>>Running test BridgeSceneApproachMethodDefined
¶<<
¶>>Running checkstyle, this will take time<<"), Diff(INSERT,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="27" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190310" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneApproachMethodDefined test execution time (ms):5331<<
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,"WARNING: A terminally deprecated method in java.lang.System has been called
¶WARNING: System::setSecurityManager has been called by unc.tools.checkstyle.NonExitingMain (file:/C:/Users/<USER>/code/Java/Isa/Comp301All.jar)
¶WARNING: Please consider reporting this to the maintainers of unc.tools.checkstyle.NonExitingMain
¶WARNING: System::setSecurityManager will be removed in a future release"), Diff(INSERT,">>BridgeSceneApproachMethodDefined test execution time (ms):5331<<"), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="28" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190320" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneApproachMethodDefined"), Diff(DELETE," test execution time (ms):5331"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="29" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190322" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test BridgeSceneSayMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Approach"), Diff(INSERT,"Say"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="30" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190401" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneSayMethodDefined test execution time (ms):80<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test BridgeSceneSayMethodDefined
¶"), Diff(INSERT,"BridgeSceneSayMethodDefined test execution time (ms):80"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="31" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190419" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test BridgeScenePassedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneSayMethodDefined"), Diff(DELETE," test execution time (ms):80"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="32" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190443" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeScenePassedMethodDefined test execution time (ms):28<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Say"), Diff(INSERT,"Passed"), Diff(EQUAL,"MethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test BridgeScenePassedMethodDefined
¶"), Diff(INSERT," test execution time (ms):28"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="33" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190448" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test BridgeSceneFailedMethodDefined
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeScenePassedMethodDefined"), Diff(DELETE," test execution time (ms):28"), Diff(INSERT,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test BridgeSceneFailedMethodDefined
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="34" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190472" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneFailedMethodDefined test execution time (ms):20<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶"), Diff(EQUAL,"BridgeScene"), Diff(DELETE,"Pass"), Diff(INSERT,"Fail"), Diff(EQUAL,"edMethodDefined"), Diff(DELETE,",100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test "), Diff(INSERT," test execution time (ms):20<<
¶>>Test Result:
¶"), Diff(EQUAL,"BridgeSceneFailedMethodDefined"), Diff(INSERT,",100.0% complete,2.0,2.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="35" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190513" type="ConsoleOutput">
    <outputString><![CDATA[>>BridgeSceneDynamics test execution time (ms):67<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>BridgeScene"), Diff(DELETE,"FailedMethodDefined"), Diff(INSERT,"Dynamics"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"20<<
¶>>Test Result:
¶BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
¶"), Diff(INSERT,"67"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="36" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190517" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"BridgeSceneDynamics"), Diff(DELETE," test execution time (ms):67"), Diff(INSERT,",100.0% complete,50.0,50.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="37" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190530" type="ConsoleOutput">
    <outputString><![CDATA[>>A2PackageDeclarations test execution time (ms):83<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶BridgeSceneDynamics,100.0% complete,50.0,50.0,
¶"), Diff(INSERT,"A2PackageDeclarations test execution time (ms):83"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="38" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190544" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2SimplifyBooleanExpressions
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2PackageDeclarations"), Diff(DELETE," test execution time (ms):83"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2SimplifyBooleanExpressions
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="39" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190549" type="ConsoleOutput">
    <outputString><![CDATA[>>A2SimplifyBooleanExpressions test execution time (ms):11<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2PackageDeclarations,100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2SimplifyBooleanExpressions
¶"), Diff(INSERT,"A2SimplifyBooleanExpressions test execution time (ms):11"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="40" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190554" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2SimplifyBooleanExpressions"), Diff(DELETE," test execution time (ms):11"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="41" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190558" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2SimplifyBooleanReturns
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="42" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190565" type="ConsoleOutput">
    <outputString><![CDATA[>>A2SimplifyBooleanReturns test execution time (ms):10<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2SimplifyBooleanReturns
¶"), Diff(INSERT,"A2SimplifyBooleanReturns test execution time (ms):10"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="43" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190569" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2SimplifyBooleanReturns"), Diff(DELETE," test execution time (ms):10"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="44" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190571" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2NoHiddenFields
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NoHiddenFields"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="45" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190578" type="ConsoleOutput">
    <outputString><![CDATA[>>A2NoHiddenFields test execution time (ms):8<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NoHiddenFields
¶"), Diff(INSERT,"A2NoHiddenFields test execution time (ms):8"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="46" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190583" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2NoHiddenFields"), Diff(DELETE," test execution time (ms):8"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="47" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190588" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2NamingConventions
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2NoHiddenFields,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NamingConventions"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="48" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190600" type="ConsoleOutput">
    <outputString><![CDATA[>>A2NamingConventions test execution time (ms):14<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NamingConventions"), Diff(INSERT,"A2NamingConventions test execution time (ms):14<<
¶>>Test Result:
¶A2NamingConventions,100.0% complete,5.0,5.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="49" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190606" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2InterfaceAsType
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"A2NamingConventions test execution time (ms):14<<
¶>>Test Result:
¶A2NamingConventions,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2InterfaceAsType"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="50" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190625" type="ConsoleOutput">
    <outputString><![CDATA[>>A2InterfaceAsType test execution time (ms):24<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2InterfaceAsType
¶"), Diff(INSERT,"A2InterfaceAsType test execution time (ms):24"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="51" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190629" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2InterfaceAsType"), Diff(DELETE," test execution time (ms):24"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="52" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190633" type="ConsoleOutput">
    <outputString><![CDATA[>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2NamedConstants
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2InterfaceAsType,100.0% complete,5.0,5.0,"), Diff(INSERT,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NamedConstants"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="53" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190646" type="ConsoleOutput">
    <outputString><![CDATA[>>A2NamedConstants test execution time (ms):16<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NamedConstants
¶"), Diff(INSERT,"A2NamedConstants test execution time (ms):16"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="54" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190662" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2NoStarImports
<<
>>A2NoStarImports test execution time (ms):5<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2PublicMethodsOverride
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2NamedConstants"), Diff(DELETE," test execution time (ms):16"), Diff(INSERT,",100.0% complete,10.0,10.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NoStarImports
¶<<
¶>>A2NoStarImports test execution time (ms):5<<
¶>>Test Result:
¶A2NoStarImports,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2PublicMethodsOverride
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="55" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190676" type="ConsoleOutput">
    <outputString><![CDATA[>>A2PublicMethodsOverride test execution time (ms):12<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2NamedConstants,100.0% complete,10.0,10.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NoStarImports
¶<<
¶>>A2NoStarImports test execution time (ms):5<<
¶>>Test Result:
¶A2NoStarImports,100.0% complete,2.0,2.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2PublicMethodsOverride
¶"), Diff(INSERT,"A2PublicMethodsOverride test execution time (ms):12"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="56" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190681" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2PublicMethodsOverride,100.0% complete,5.0,5.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2MnemonicNames
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2PublicMethodsOverride"), Diff(DELETE," test execution time (ms):12"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2MnemonicNames
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="57" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190727" type="ConsoleOutput">
    <outputString><![CDATA[>>A2MnemonicNames test execution time (ms):34<<
>>Test Result:
A2MnemonicNames,100.0% complete,10.0,10.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2Encapsulation
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2PublicMethodsOverride"), Diff(INSERT,"A2MnemonicNames test execution time (ms):34<<
¶>>Test Result:
¶A2MnemonicNames"), Diff(EQUAL,",100.0% complete,"), Diff(DELETE,"5"), Diff(INSERT,"10"), Diff(EQUAL,".0,"), Diff(DELETE,"5"), Diff(INSERT,"10"), Diff(EQUAL,".0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2"), Diff(DELETE,"MnemonicNames"), Diff(INSERT,"Encapsulation"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="58" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190736" type="ConsoleOutput">
    <outputString><![CDATA[>>A2Encapsulation test execution time (ms):7<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2NonPublicAccessModifiersMatched
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>A2"), Diff(DELETE,"MnemonicNames"), Diff(INSERT,"Encapsulation"), Diff(EQUAL," test execution time (ms):"), Diff(DELETE,"34"), Diff(INSERT,"7"), Diff(EQUAL,"<<
¶>>Test Result:
¶A2"), Diff(DELETE,"MnemonicNames"), Diff(INSERT,"Encapsulation"), Diff(EQUAL,",100.0% complete,"), Diff(DELETE,"10"), Diff(INSERT,"5"), Diff(EQUAL,".0,"), Diff(DELETE,"10"), Diff(INSERT,"5"), Diff(EQUAL,".0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2"), Diff(DELETE,"Encapsulation"), Diff(INSERT,"NonPublicAccessModifiersMatched"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="59" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190752" type="ConsoleOutput">
    <outputString><![CDATA[>>A2NonPublicAccessModifiersMatched test execution time (ms):16<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>A2"), Diff(DELETE,"Encapsulation test execution time (ms):7<<
¶>>Test Result:
¶A2Encapsulation,100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2NonPublicAccessModifiersMatched
¶"), Diff(INSERT,"NonPublicAccessModifiersMatched test execution time (ms):16"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="60" _type="ConsoleOutput" date="Wed Jul 09 21:09:22 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190757" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Wed Jul 09 21:09:22 EDT 2025<<
>>Running test A2CommonPropertiesAreInherited
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2NonPublicAccessModifiersMatched"), Diff(DELETE," test execution time (ms):16"), Diff(INSERT,",100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test A2CommonPropertiesAreInherited
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="61" _type="ConsoleOutput" date="Wed Jul 09 21:09:23 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190800" type="ConsoleOutput">
    <outputString><![CDATA[>>A2CommonPropertiesAreInherited test execution time (ms):45<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
¶<<
¶>>Wed Jul 09 21:09:22 EDT 2025<<
¶>>Running test "), Diff(EQUAL,"A2CommonPropertiesAreInherited"), Diff(DELETE,"
¶"), Diff(INSERT," test execution time (ms):45"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="62" _type="ConsoleOutput" date="Wed Jul 09 21:09:23 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190812" type="ConsoleOutput">
    <outputString><![CDATA[>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Steps traced since last test:

>>Wed Jul 09 21:09:23 EDT 2025<<
>>Running test A2CommonSignaturesAreInherited
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(INSERT,"Test Result:
¶"), Diff(EQUAL,"A2CommonPropertiesAreInherited"), Diff(DELETE," test execution time (ms):45"), Diff(INSERT,",0.0% complete,0.0,7.0,See console trace about lines failing  this check
¶<<
¶>>Steps traced since last test:
¶
¶>>Wed Jul 09 21:09:23 EDT 2025<<
¶>>Running test A2CommonSignaturesAreInherited
¶"), Diff(EQUAL,"<<
¶")]]]></diff>
  </Command>
  <Command __id="63" _type="ConsoleOutput" date="Wed Jul 09 21:09:23 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="190881" type="ConsoleOutput">
    <outputString><![CDATA[>>A2CommonSignaturesAreInherited test execution time (ms):61<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
]]></outputString>
    <diff><![CDATA[[Diff(EQUAL,">>"), Diff(DELETE,"Test Result:
¶A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
¶<<
¶>>Steps traced since last test:
¶
¶>>Wed Jul 09 21:09:23 EDT 2025<<
¶>>Running test "), Diff(INSERT,"A2CommonSignaturesAreInherited test execution time (ms):61<<
¶>>Test Result:
¶"), Diff(EQUAL,"A2CommonSignaturesAreInherited"), Diff(INSERT,",100.0% complete,7.0,7.0,"), Diff(EQUAL,"
¶<<
¶")]]]></diff>
  </Command>
  <Command __id="64" _type="ConsoleOutput" date="Wed Jul 09 21:09:35 EDT 2025" overflow="false" starttimestamp="1752109572204" timestamp="203077" type="ConsoleOutput">
    <outputString><![CDATA[Re-running test gradingTools.comp301ss21.assignment2.testcases.style.A2CommonPropertiesAreInherited@fee4c18 . Results may change.
]]></outputString>
    <diff><![CDATA[[Diff(DELETE,">>A2CommonSignaturesAreInherited test execution time (ms):61<<
¶>>Test Result:
¶A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
¶<<"), Diff(INSERT,"Re-running test gradingTools.comp301ss21.assignment2.testcases.style.A2CommonPropertiesAreInherited@fee4c18 . Results may change."), Diff(EQUAL,"
¶")]]]></diff>
  </Command>
  <Command __id="65" _type="ShellCommand" date="Wed Jul 09 21:09:59 EDT 2025" starttimestamp="1752109572204" timestamp="226811" type="ECLIPSE_GAINED_FOCUS" />
