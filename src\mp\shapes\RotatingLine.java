package mp.shapes;
import util.annotations.Visible;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
import util.models.PropertyListenerRegisterer;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.List;
import java.util.ArrayList;
@Tags({Comp301Tags.LOCATABLE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class RotatingLine implements RotateLine, PropertyListenerRegisterer{
    private PolarPointInterface point;
    private int x, y;
    private static final double UNIT = Math.PI / 32;
    @Visible(false)
    private final List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
    @Override
	public void addPropertyChangeListener(PropertyChangeListener arg0) {
		propertyChangeListeners.add(arg0);
	}
    @Override
    public void notify(String property, Object old, Object current) {
        PropertyChangeEvent event = new PropertyChangeEvent(this, property, old, current);
        for (PropertyChangeListener some : propertyChangeListeners) {some.propertyChange(event);}
    }
    public RotatingLine() {
        this.x = 0;
        this.y = 0;
        this.point = new APolarPoint(0, 0);
    }
    @Override
    public int getX() {
        return x + point.getX();
    }
    @Override
    public void setX(final int ax) {
    	notify("X",this.x,ax);
    	x = ax;
    }
    @Override
    public int getY() {
        return y + point.getY();
    }
    @Override
    public void setY(final int ay) {
    	notify("Y",this.y,ay);
        y = ay;
    }
    @Override
    public int getWidth() {
        return point.getX();
    }
    @Override
    public int getHeight() {
        return point.getY();
    }
    @Override
    public void setHeight(int someHeight){notify("Height",getHeight(),someHeight);}
    @Override
    public void setWidth(int someWidth){notify("Width",getWidth(),someWidth);}
    @Override
    public double getRadius() {
        return point.getRadius();
    }
    @Override
    public void setRadius(final double radius) {
        point = new APolarPoint(radius, point.getAngle());
    }
    @Override
    public double getAngle() {
        return point.getAngle();
    }
    @Override
    public void setAngle(final double angle) {
        point = new APolarPoint(point.getRadius(), angle);
    }
    @Override
    public void rotate(final int units) {
        setAngle(point.getAngle() + units * UNIT);
    }
    @Override
    public void move(final int deltaX, final int deltaY) {
    	int height = getHeight();
    	int width = getWidth();
        setX(x + deltaX);
        setY(y + deltaY);
        notify("Width",width,getWidth());
        notify("Height",height,getHeight());
    }
}
