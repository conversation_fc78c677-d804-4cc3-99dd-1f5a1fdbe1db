package mp.shapes;
import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import tags301.Comp301Tags;
@Tags({Comp301Tags.BOUNDED_SHAPE})
@StructurePattern(StructurePatternNames.LINE_PATTERN)
public class AScaleRectangle implements AScaleRectangleInterface{
	int x, y, width, height;
	int percentConversion = 100;
	public AScaleRectangle(int a, int b, int w, int h) {
		this.x = a;
		this.y = b;
		this.width = w;
		this.height = h;
	}
	@Override
	public int getX() {return x;}
	@Override
	public int getY() {return y;}
	@Override
	public int getWidth() {return width;}	
	@Override
	public int getHeight() { return height;}
	@Override
	public void setHeight(int someHeight) {height = someHeight;}
	@Override
	public void setWidth(int someWidth) {width = someWidth;}
	@Override
	public void scale(int percentage){
		width = (width*percentage)/percentConversion;
		height = (height*percentage)/percentConversion;		
	}
	@Override
	public void setX(int someX) {
		x = someX;
	}
	@Override
	public void setY(int someY) {
		y = someY;
	}
}