	<module name="ClassDefined">
		<property name="severity" value="info"/>
		<property name="expectedTypes" value="
			@Comp301Tags.ANGLE,
			@Comp301Tags.AVATAR,
			@Comp301Tags.BOUNDED_SHAPE,
			@<EMAIL>,
			@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.CONSOLE_SCENE_VIEW,
			@Comp301Tags.FACTORY_CLASS,
			@Comp301Tags.LOCATABLE,
			@main.Assignment2,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedSignatures" value="
			animateLine:->.*,
			main:String[]->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@main.Assignment2"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			@Comp301Tags.FACTORY_CLASS!legsFactoryMethod:->@Comp301Tags.ANGLE,
			bus.uigen.ObjectEditor!edit:Object->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.FACTORY_CLASS!bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			bus.uigen.OEFrame!refresh:->void,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
			java.lang.Thread!sleep:long->void,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value="
			KnightTurn:boolean,
			Gorge:.*,
			Occupied:boolean,
			Arthur:@Comp301Tags.AVATAR,
			KnightArea:@Comp301Tags.BOUNDED_SHAPE,
			Guard:@Comp301Tags.AVATAR,
			Lancelot:@Comp301Tags.AVATAR,
			GuardArea:@Comp301Tags.BOUNDED_SHAPE,
			Galahad:@Comp301Tags.AVATAR,
			Robin:@Comp301Tags.AVATAR,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedSignatures" value="
			failed:->void,
			passed:->void,
			approach:@Comp301Tags.AVATAR->void,
			say:String->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BRIDGE_SCENE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.AVATAR!getHead:->@Comp301Tags.BOUNDED_SHAPE,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.AVATAR!getStringShape:->.*,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedSignatures" value="
			consoleSceneViewFactoryMethod:->@Comp301Tags.CONSOLE_SCENE_VIEW,
			legsFactoryMethod:->@Comp301Tags.ANGLE,
			bridgeSceneFactoryMethod:->@Comp301Tags.BRIDGE_SCENE,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.FACTORY_CLASS"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			@Comp301Tags.LOCATABLE!setAngle:double->void,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.LOCATABLE!setRadius:double->void,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			java.lang.Math!cos:double->double,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			java.lang.Math!sqrt:double->double,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:.*,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			Legs:@Comp301Tags.ANGLE,
			Head:@Comp301Tags.BOUNDED_SHAPE,
			StringShape:.*,
			Arms:@Comp301Tags.ANGLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			@Comp301Tags.ANGLE!move:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.AVATAR"/>
		<property name="expectedProperties" value="
			ImageFileName:String,
			X:int,
			Y:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.LOCATABLE,
			LeftLine:@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value="
			RightLine:@Comp301Tags.LOCATABLE,
			LeftLine:@Comp301Tags.LOCATABLE,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedProperties" value=""/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedSignatures" value="
			move:int;int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.ANGLE"/>
		<property name="expectedCalls" value="
			@Comp301Tags.LOCATABLE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.LOCATABLE!setY:*->.*,
			@Comp301Tags.LOCATABLE!getHeight:->int,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.LOCATABLE!move:*->.*,
			@Comp301Tags.AVATAR!getX:*->.*,
			@Comp301Tags.AVATAR!setY:*->.*,
			@Comp301Tags.AVATAR!setX:*->.*,
			@Comp301Tags.LOCATABLE!notify:String;Object;Object->void,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!cos:double->double,
			@Comp301Tags.AVATAR!move:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!setX:*->.*,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
			@Comp301Tags.LOCATABLE!getWidth:->int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedProperties" value="
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.BOUNDED_SHAPE"/>
		<property name="expectedSignatures" value="
			scale:int->void,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedInterfaces">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedInterfaces" value="
			util.models.PropertyListenerRegisterer,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedGetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSetters">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedProperties" value="
			Radius:double,
			Angle:double,
			X:int,
			Y:int,
			Height:int,
			Width:int,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			rotate:int->void,
		"/>
	</module>
	<module name="ExpectedSignatures">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedSignatures" value="
			notify:String;Object;Object->void,
			move:int;int->void,
			addPropertyChangeListener:java.beans.PropertyChangeListener->void,
			rotate:int->void,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="MissingMethodCall">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="expectedCalls" value="
			java.util.List!add:Object->boolean,
			@Comp301Tags.AVATAR!getX:*->.*,
			java.lang.Math!atan:double->double,
			@Comp301Tags.BOUNDED_SHAPE!getY:*->.*,
			@Comp301Tags.LOCATABLE!getX:*->.*,
			@Comp301Tags.AVATAR!getY:*->.*,
			java.lang.Math!sin:double->double,
			@Comp301Tags.LOCATABLE!getY:*->.*,
			java.lang.Math!cos:double->double,
			java.lang.Math!sqrt:double->double,
			java.beans.PropertyChangeListener!propertyChange:java.beans.PropertyChangeEvent->void,
			@Comp301Tags.BOUNDED_SHAPE!getX:*->.*,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="warning"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
	<module name="ExpectedClassInstantiations">
		<property name="severity" value="info"/>
		<property name="includeTypeTags" value="@Comp301Tags.LOCATABLE"/>
		<property name="instantiations" value="
			java.util.ArrayList,
		"/>
	</module>
