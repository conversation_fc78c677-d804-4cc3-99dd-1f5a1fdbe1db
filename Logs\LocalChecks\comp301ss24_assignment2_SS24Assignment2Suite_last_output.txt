*PRE_OUTPUT*
*OUTPUT*
*ERROR*

*POST_OUTPUT*
>>Test Result:
BridgeSceneApproachMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneSayMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeScenePassedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneFailedMethodDefined,100.0% complete,2.0,2.0,
<<
>>Test Result:
BridgeSceneDynamics,100.0% complete,50.0,50.0,
<<
>>Test Result:
A2PackageDeclarations,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanExpressions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2SimplifyBooleanReturns,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NoHiddenFields,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamingConventions,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2InterfaceAsType,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NamedConstants,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2NoStarImports,100.0% complete,2.0,2.0,
<<
>>Test Result:
A2PublicMethodsOverride,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2MnemonicNames,100.0% complete,10.0,10.0,
<<
>>Test Result:
A2Encapsulation,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2NonPublicAccessModifiersMatched,100.0% complete,5.0,5.0,
<<
>>Test Result:
A2CommonPropertiesAreInherited,0.0% complete,0.0,7.0,See console trace about lines failing  this check
<<
>>Test Result:
A2CommonSignaturesAreInherited,100.0% complete,7.0,7.0,
<<
*END_OUTPUT*
