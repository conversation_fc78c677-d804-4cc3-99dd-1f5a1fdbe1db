package mp.bridge;

import util.annotations.Tags;
import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class ArthurHead implements ImageShape {
    private String location = "images/arthur.jpg";
    private int x, y;
    public ArthurHead() {
    }
    @Override
    public String getImageFileName() { 
    	return location; 
    }
    @Override
    public void setImageFileName(String file) { 
    	location = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int someX) {
    	x = someX; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int someY) { 
    	y = someY; 
    }
}
