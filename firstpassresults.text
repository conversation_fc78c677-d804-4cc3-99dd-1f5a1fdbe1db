Starting audit...
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:0: Expected type names/tags: [main.Assignment1, @Comp301Tags.ROTATING_LINE, @Comp301Tags.ANGLE, @Comp301Tags.AVATAR, @Comp301Tags.BRIDGE_SCENE, main.Assignment2, @Comp301Tags.LOCATABLE, @Comp301Tags.BOUNDED_SHAPE, @Comp301Tags.FACTORY_CLASS, @Comp301Tags.CONSOLE_SCENE_VIEW, main.Assignment3, @Comp301Tags.INHERITING_BRIDGE_SCENE_PAINTER, @Comp301Tags.PAINT_LISTENER, @Comp301Tags.OBSERVABLE_BRIDGE_SCENE_PAINTER, @Comp301Tags.BRIDGE_SCENE_CONTROLLER, @Comp301Tags.TABLE, @Comp301Tags. PROGRESS_BAR_CREATOR] [STBuilder]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:10:29: Named Constant SOME_RADIUS defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:11:32: Named Constant SOME_ANGLE defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:12:29: Named Constant START_X defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:13:29: Named Constant START_Y defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:14:29: Named Constant DELTA defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:15:29: Named Constant COUNT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:16:30: Named Constant SLEEP_SECONDS defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\Assignment2.java:32:29: Parameter args should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:18:1: Class Data Abstraction Coupling is 8 (max allowed is 7) classes [AScaleRectangle, ArthurHead, AvatarImpl, GalahadHead, Gorge, GuardHead, LancelotHead, RobinHead]. [ClassDataAbstractionCoupling]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:22:29: Named Constant SOME_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:23:29: Named Constant SOME_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:24:29: Named Constant LANCELOT_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:25:29: Named Constant ROBIN_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:26:29: Named Constant GAL_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:27:29: Named Constant GUARD_CONSTANT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:33:30: Named Constant AREA_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:34:30: Named Constant KNIGHT_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:35:30: Named Constant GUARD_AY defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:36:30: Named Constant AREA_WIDTH defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:37:30: Named Constant AREA_HEIGHT defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:39:30: Named Constant GORGE_AX defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:78:26: Final parameter avatar defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\BridgeSceneImpl.java:84:21: Final parameter say defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:7:35: Named Constant MAX_PRINTED_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:8:43: Named Constant MAX_TRACES defined. Good! [ConstantDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:9:43: Named Constant PROCESS_TIME defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\main\RunSS25A2Tests.java:10:41: Parameter args should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:6:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:20:34: Parameter file should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:28:22: Parameter someX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\ArthurHead.java:36:22: Parameter someY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:7:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:8:8: Unused import - mp.shapes.Moveable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:18:23: Final parameter h defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\AvatarImpl.java:44:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:5:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:19:34: Final parameter file defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:27:22: Final parameter someX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GalahadHead.java:35:22: Final parameter someY defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:18:34: Final parameter file defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:26:22: Parameter someX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\GuardHead.java:34:22: Parameter someY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:6:8: Unused import - mp.shapes.Locatable. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:20:34: Parameter file should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:28:22: Parameter someX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\LancelotHead.java:36:22: Parameter someY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:19:34: Parameter file should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:27:22: Parameter someX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\RobinHead.java:35:22: Parameter someY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:2:8: Unused import - mp.shapes.Get. [UnusedImports]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:27:22: Parameter deltaX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\Shape.java:27:34: Parameter deltaY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:13:25: Parameter at should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:21:22: Final parameter ax defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\bridge\SpeechBubble.java:29:22: Final parameter ay defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:5:28: Parameter theRadius should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:5:46: Parameter theAngle should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:9:28: Parameter theX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:9:38: Parameter theY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:22:26: Parameter x should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\APolarPoint.java:25:26: Parameter y should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:32: Parameter a should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:39: Parameter b should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:46: Parameter w should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:11:53: Parameter h should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:26:31: Parameter someHeight should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:28:30: Parameter someWidth should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:30:27: Parameter percentage should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:35:26: Parameter someX should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\AScaleRectangle.java:39:26: Parameter someY should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\ConsoleScene.java:8:36: Parameter evt should be final. [FinalParameters]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\Gorge.java:14:18: Final parameter x defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:17:33: Named Constant UNIT defined. Good! [ConstantDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:21:47: Parameter arg0 should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:24: Parameter property should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:41: Parameter old should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:25:53: Parameter current should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:26:29: Variable 'event' should be declared final. [FinalLocalVariable]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:39:22: Final parameter ax defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:48:22: Final parameter ay defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:61:27: Parameter someHeight should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:61:50: The String "Height" appears 2 times in the file. [MultipleStringLiterals]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63:26: Parameter someWidth should be final. [FinalParameters]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:63:48: The String "Width" appears 2 times in the file. [MultipleStringLiterals]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:69:27: Final parameter radius defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:77:26: Final parameter angle defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:81:24: Final parameter units defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:85:22: Final parameter deltaX defined. Good! [FinalParameterDefined]
[INFO] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:85:40: Final parameter deltaY defined. Good! [FinalParameterDefined]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:86:13: Variable 'height' should be declared final. [FinalLocalVariable]
[WARN] C:\Users\<USER>\code\Java\Isa\Assn2\.\src\mp\shapes\RotatingLine.java:87:13: Variable 'width' should be declared final. [FinalLocalVariable]
Audit done.
