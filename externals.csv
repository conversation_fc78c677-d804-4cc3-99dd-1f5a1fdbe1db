Caller Type,Caller Type Words,Caller Tag,Caller Tag Words,Caller Method,Caller Method Words,Caller Super Types,Calling Super Types Words,Called  Type,Called Type Words,Called  Tagged Type,Called Tagged Type Words,Called Method,Called Method Words
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,passed,passed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,move,move
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setX,set:x
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.<PERSON><PERSON><PERSON>_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getHead,get:head
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setY,set:y
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getHead,get:head
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setX,set:x
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getHead,get:head
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setY,set:y
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,failed,failed,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getHead,get:head
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,approach,approach,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,move,move
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,say,say,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setText,set:text
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,say,say,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getStringShape,get:string:shape
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,say,say,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,setText,set:text
main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,say,say,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,mp.bridge.Avatar,avatar,@Comp301Tags.AVATAR,comp:tags:avatar,getStringShape,get:string:shape
mp.bridge.Shape,shape,@Comp301Tags.ANGLE,comp:tags:angle,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move
mp.bridge.Shape,shape,@Comp301Tags.ANGLE,comp:tags:angle,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,RotatingLine,rotating:line
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setRadius,set:radius
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setAngle,set:angle
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setX,set:x
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setY,set:y
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,bus.uigen.ObjectEditor,object:editor,bus.uigen.ObjectEditor,object:editor,edit,edit
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,mp.shapes.RotateLine,rotate:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,bus.uigen.OEFrame,frame,bus.uigen.OEFrame,frame,refresh,refresh
main.Assignment2,assignment,@main.Assignment2,main:assignment,animateLine,animate:line,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,Thread,thread,java.lang.Thread,thread,sleep,sleep
main.Assignment2,assignment,@main.Assignment2,main:assignment,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,main.StaticFactoryClass,static:factory:class,@Comp301Tags.FACTORY_CLASS,comp:tags:factory:class,bridgeSceneFactoryMethod,bridge:scene:factory:method
main.Assignment2,assignment,@main.Assignment2,main:assignment,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE,comp:tags:bridge:scene:comp:tags:angle,bus.uigen.ObjectEditor,object:editor,bus.uigen.ObjectEditor,object:editor,edit,edit
mp.shapes.ConsoleScene,console:scene,ConsoleScene,console:scene,propertyChange,property:change,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener,java.io.PrintStream,print:stream,java.io.PrintStream,print:stream,println,none
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.ImageShape,image:shape,@Comp301Tags.BOUNDED_SHAPE,comp:tags:bounded:shape,setX,set:x
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.ImageShape,image:shape,@Comp301Tags.BOUNDED_SHAPE,comp:tags:bounded:shape,getX,get:x
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.ImageShape,image:shape,@Comp301Tags.BOUNDED_SHAPE,comp:tags:bounded:shape,setY,set:y
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.ImageShape,image:shape,@Comp301Tags.BOUNDED_SHAPE,comp:tags:bounded:shape,getY,get:y
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.Angle,angle,@Comp301Tags.ANGLE,comp:tags:angle,move,move
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.Angle,angle,@Comp301Tags.ANGLE,comp:tags:angle,move,move
mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar,mp.bridge.AvatarImpl,avatar:impl,@Comp301Tags.AVATAR,comp:tags:avatar,layoutAtOrigin,layout:at:origin
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,addPropertyChangeListener,add:property:change:listener,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,java.util.List,list,java.util.List,list,add,add
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,java.beans.PropertyChangeEvent,property:change:event,java.beans.PropertyChangeEvent,property:change:event,PropertyChangeEvent,property:change:event
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,java.beans.PropertyChangeListener,property:change:listener,java.beans.PropertyChangeListener,property:change:listener,propertyChange,property:change
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setX,set:x,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setY,set:y,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setHeight,set:height,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setHeight,set:height,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getHeight,get:height
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setWidth,set:width,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setWidth,set:width,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getWidth,get:width
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,rotate,rotate,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setAngle,set:angle
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getHeight,get:height
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getWidth,get:width
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setX,set:x
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,setY,set:y
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getWidth,get:width
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,notify,notify
mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,move,move,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.shapes.RotatingLine,rotating:line,@Comp301Tags.LOCATABLE,comp:tags:locatable,getHeight,get:height
main.StaticFactoryClass,static:factory:class,@Comp301Tags.FACTORY_CLASS,comp:tags:factory:class,bridgeSceneFactoryMethod,bridge:scene:factory:method,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,main.BridgeSceneImpl,bridge:scene:impl,@Comp301Tags.BRIDGE_SCENE,comp:tags:bridge:scene,BridgeSceneImpl,bridge:scene:impl
main.StaticFactoryClass,static:factory:class,@Comp301Tags.FACTORY_CLASS,comp:tags:factory:class,legsFactoryMethod,legs:factory:method,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener,mp.bridge.Shape,shape,@Comp301Tags.ANGLE,comp:tags:angle,Shape,shape
mp.shapes.APolarPoint,a:polar:point,APolarPoint,a:polar:point,getX,get:x,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,Math,math,java.lang.Math,math,cos,none
mp.shapes.APolarPoint,a:polar:point,APolarPoint,a:polar:point,getY,get:y,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,Math,math,java.lang.Math,math,sin,sin
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,setTracerShowInfo,set:tracer:show:info
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,setBufferTracedMessages,set:buffer:traced:messages
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,setMaxPrintedTraces,set:max:printed:traces
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,trace.grader.basics.GraderBasicsTraceUtility,grader:basics:trace:utility,setMaxTraces,set:max:traces
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,grader.basics.execution.BasicProjectExecution,basic:project:execution,grader.basics.execution.BasicProjectExecution,basic:project:execution,setProcessTimeOut,set:process:time:out
main.RunSS25A2Tests,run:a:tests,RunSS25A2Tests,run:a:tests,main,main,@Comp301Tags.BRIDGE_SCENE:@Comp301Tags.ANGLE:@Comp301Tags.CONSOLE_SCENE_VIEW:PropertyChangeListener:EventListener:@Comp301Tags.AVATAR:@Comp301Tags.LOCATABLE:@<EMAIL>:@Comp301Tags.LOCATABLE:PropertyListenerRegisterer:@Comp301Tags.LOCATABLE,comp:tags:bridge:scene:comp:tags:angle:comp:tags:console:scene:view:property:change:listener:event:listener:comp:tags:avatar:comp:tags:locatable:comp:tags:bounded:shape:comp:tags:locatable:comp:tags:locatable:property:listener:comp:tags:locatable,gradingTools.comp301ss24.assignment2.SS24Assignment2Suite,assignment:suite,gradingTools.comp301ss24.assignment2.SS24Assignment2Suite,assignment:suite,main,main
