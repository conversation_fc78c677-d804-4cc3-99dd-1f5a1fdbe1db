package mp.bridge;

import util.annotations.StructurePattern;
import util.annotations.StructurePatternNames;
import util.annotations.Tags;
import mp.shapes.Locatable;
import tags301.Comp301Tags;
@Tags(Comp301Tags.AVATAR)
@StructurePattern(StructurePatternNames.IMAGE_PATTERN)
public class LancelotHead implements ImageShape{
    private String location = "images/lancelot.jpg";
    private int x, y;
    public LancelotHead() {
    }
    @Override
    public String getImageFileName() { 
    	return location; 
    }
    @Override
    public void setImageFileName(String file) { 
    	location = file; 
    }
    @Override
    public int getX() { 
    	return x; 
    }
    @Override
    public void setX(int someX) {
    	this.x = someX; 
    }
    @Override
    public int getY() {
    	return y; 
    }
    @Override
    public void setY(int someY) { 
    	this.y = someY; 
    }
}
